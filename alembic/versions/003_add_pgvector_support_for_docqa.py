"""Add pgvector support for docqa system

Revision ID: 003_add_pgvector_support_for_docqa
Revises: bcb1046177f2
Create Date: 2025-01-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from pgvector.sqlalchemy import Vector


# revision identifiers, used by Alembic.
revision = '003pgvector'
down_revision = 'bcb1046177f2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add embedding columns (as TEXT initially, will be converted to vector type later)"""

    # Add embedding column to documents table as TEXT initially
    # This will be converted to vector type once pgvector extension is available
    op.add_column('documents', sa.Column('embedding', sa.Text(), nullable=True))

    # Add embedding column to franchisors table as TEXT initially
    op.add_column('franchisors', sa.Column('embedding', sa.Text(), nullable=True))

    print("✓ Embedding columns added as TEXT type")
    print("⚠️  To enable vector functionality, please:")
    print("   1. Ask your database administrator to run: CREATE EXTENSION IF NOT EXISTS vector;")
    print("   2. Run the setup script: python setup_pgvector_columns.py")


def downgrade() -> None:
    """Remove embedding columns"""

    # Drop embedding columns
    op.drop_column('franchisors', 'embedding')
    op.drop_column('documents', 'embedding')
