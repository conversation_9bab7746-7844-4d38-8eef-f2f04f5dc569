#!/usr/bin/env python3
"""
Script to check database schema and specifically check for embedding column
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def check_database_schema():
    # Parse the DATABASE_URL to get connection parameters
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("DATABASE_URL not found in environment")
        return
    
    # Convert asyncpg URL to regular postgres URL for asyncpg
    database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    
    try:
        # Connect to database
        conn = await asyncpg.connect(database_url)
        
        # Check if documents table exists
        tables_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name;
        """
        
        tables = await conn.fetch(tables_query)
        print("Existing tables:")
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # Check documents table columns specifically
        if any(table['table_name'] == 'documents' for table in tables):
            print("\nDocuments table columns:")
            columns_query = """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'documents' 
            ORDER BY ordinal_position;
            """
            
            columns = await conn.fetch(columns_query)
            for column in columns:
                print(f"  - {column['column_name']}: {column['data_type']} ({'NULL' if column['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            # Check specifically for embedding column
            embedding_exists = any(col['column_name'] == 'embedding' for col in columns)
            print(f"\nEmbedding column exists: {embedding_exists}")
        else:
            print("\nDocuments table does not exist")
        
        # Check alembic version table
        alembic_query = """
        SELECT version_num 
        FROM alembic_version 
        ORDER BY version_num;
        """
        
        try:
            versions = await conn.fetch(alembic_query)
            print(f"\nCurrent alembic versions:")
            for version in versions:
                print(f"  - {version['version_num']}")
        except asyncpg.exceptions.UndefinedTableError:
            print("\nAlembic version table does not exist")
        
        await conn.close()
        
    except Exception as e:
        print(f"Error connecting to database: {e}")

if __name__ == "__main__":
    asyncio.run(check_database_schema())
